const express = require('express');
const { check } = require('express-validator');
const {
  getAllBids,
  getBidById,
  approveBid,
  rejectBid,
  deleteBid,
  bulkApproveBids,
  bulkRejectBids,
  bulkDeleteBids,
  getBidStats,
  exportBids,
  updateBidStatus,
  cancelBid,
  flagBid,
  unflagBid,
  getAuctionStats
} = require('../../controllers/admin/bids');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin bid routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all bids with filtering, sorting, and pagination
router.get('/', getAllBids);

// Get bid statistics
router.get('/stats', getBidStats);

// Get auction statistics
router.get('/auction-stats', getAuctionStats);

// Export bids data
router.get('/export', exportBids);

// Bulk operations
router.post('/bulk-approve', [
  check('bidIds', 'Bid IDs array is required').isArray(),
], bulkApproveBids);

router.post('/bulk-reject', [
  check('bidIds', 'Bid IDs array is required').isArray(),
  check('reason', 'Rejection reason is required').not().isEmpty(),
], bulkRejectBids);

router.post('/bulk-delete', [
  check('bidIds', 'Bid IDs array is required').isArray(),
], bulkDeleteBids);

// Individual bid operations
router.get('/:id', convertS3UrlsToSigned, getBidById);
router.delete('/:id', deleteBid);

// Bid approval workflow
router.put('/:id/approve', [
  check('approvalNotes', 'Approval notes are optional').optional(),
], approveBid);

router.put('/:id/reject', [
  check('reason', 'Rejection reason is required').not().isEmpty(),
  check('rejectionNotes', 'Rejection notes are optional').optional(),
], rejectBid);

// Bid status management
router.put('/:id/status', [
  check('status', 'Status is required').isIn(['Active', 'Outbid', 'Won', 'Lost', 'Cancelled', 'Flagged']),
], updateBidStatus);

// Bid moderation
router.put('/:id/cancel', [
  check('reason', 'Cancellation reason is required').not().isEmpty(),
], cancelBid);

router.put('/:id/flag', [
  check('reason', 'Flag reason is required').not().isEmpty(),
], flagBid);

router.put('/:id/unflag', unflagBid);

module.exports = router;
