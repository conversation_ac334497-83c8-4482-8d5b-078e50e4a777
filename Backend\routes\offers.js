const express = require('express');
const { check } = require('express-validator');
const {
  getOffers,
  getOffer,
  createOffer,
  updateOfferStatus,
  getBuyerOffers,
  getSellerOffers,
  getContentOffers,
  cancelOffer
} = require('../controllers/offers');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protected routes
router.use(protect);

// Buyer routes
router.get('/buyer', authorize('buyer', 'admin'), getBuyerOffers);
router.post(
  '/',
  authorize('buyer'),
  [
    check('contentId', 'Content ID is required').not().isEmpty(),
    check('amount', 'Amount is required and must be a positive number').isFloat({ min: 0.01 }),
    check('message', 'Message cannot be more than 500 characters').optional().isLength({ max: 500 })
  ],
  createOffer
);
router.put('/:id/cancel', authorize('buyer'), cancelOffer);

// Seller routes
router.get('/seller', authorize('seller', 'admin'), convertS3UrlsToSigned, getSellerOffers);
router.put(
  '/:id/status',
  authorize('seller', 'admin'),
  [
    check('status', 'Status must be either accepted or rejected').isIn(['accepted', 'rejected']),
    check('sellerResponse', 'Response cannot be more than 500 characters').optional().isLength({ max: 500 })
  ],
  updateOfferStatus
);

// Content-specific routes (accessible to content owner and admins)
router.get('/content/:contentId', convertS3UrlsToSigned, getContentOffers);

// Common routes
router.get('/:id', convertS3UrlsToSigned, getOffer);

// Admin routes
router.get('/', authorize('admin'), convertS3UrlsToSigned, getOffers);

module.exports = router;
