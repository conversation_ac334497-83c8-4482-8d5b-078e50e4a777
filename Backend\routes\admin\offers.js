const express = require('express');
const { check } = require('express-validator');
const {
  getAllOffers,
  getOfferById,
  updateOfferStatus,
  deleteOffer,
  bulkUpdateOffers,
  bulkDeleteOffers,
  getOfferStats,
  exportOffers,
  getOfferAnalytics,
  flagOffer,
  unflagOffer,
  moderateOffer,
  getOfferTimeline
} = require('../../controllers/admin/offers');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin offer routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get all offers with filtering, sorting, and pagination
router.get('/', getAllOffers);

// Get offer statistics
router.get('/stats', getOfferStats);

// Get offer analytics
router.get('/analytics', [
  check('period', 'Period must be valid').optional().isIn(['7d', '30d', '90d', '1y', 'all']),
  check('groupBy', 'Group by must be valid').optional().isIn(['day', 'week', 'month']),
], getOfferAnalytics);

// Export offers data
router.get('/export', convertS3UrlsToSigned, exportOffers);

// Bulk operations
router.post('/bulk-update', [
  check('offerIds', 'Offer IDs array is required').isArray(),
  check('status', 'Status is required').isIn(['Pending', 'Accepted', 'Rejected', 'Cancelled', 'Expired']),
], bulkUpdateOffers);

router.post('/bulk-delete', [
  check('offerIds', 'Offer IDs array is required').isArray(),
], bulkDeleteOffers);

// Individual offer operations
router.get('/:id', convertS3UrlsToSigned, getOfferById);

router.put('/:id/status', [
  check('status', 'Status is required').isIn(['Pending', 'Accepted', 'Rejected', 'Cancelled', 'Expired']),
  check('notes', 'Notes must be a string').optional().isString(),
], updateOfferStatus);

router.get('/:id/timeline', getOfferTimeline);

router.put('/:id/flag', [
  check('reason', 'Flag reason is required').not().isEmpty(),
], flagOffer);

router.put('/:id/unflag', unflagOffer);

router.put('/:id/moderate', [
  check('action', 'Moderation action is required').isIn(['approve', 'reject', 'flag']),
  check('reason', 'Reason is required for rejection or flagging').optional().not().isEmpty(),
], moderateOffer);

router.delete('/:id', deleteOffer);

module.exports = router;
